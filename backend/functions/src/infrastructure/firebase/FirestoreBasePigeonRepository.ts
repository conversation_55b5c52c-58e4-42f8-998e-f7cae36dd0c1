import * as admin from "firebase-admin";
import { BasePigeonRepository } from "../../domain/repositories/BasePigeonRepository";
import { BasePigeon } from "../../domain/value-objects/BasePigeon";

export class FirestoreBasePigeonRepository implements BasePigeonRepository {
    private collection = admin.firestore().collection("basepigeons");

    async getAll() {
        const pigeons: BasePigeon[] = [];
        await this.collection.get().then((snapshot) => {
            snapshot.forEach((doc) => {
                const pigeon = doc.data() as BasePigeon;
                pigeons.push(pigeon);
            });
        });
        return pigeons;
    }

    async getById(id: string): Promise<BasePigeon | null> {
        const doc = await this.collection.doc(id).get();
        if (!doc.exists) {
            return null;
        }
        return doc.data() as BasePigeon;
    }

    async add(pigeon: BasePigeon): Promise<void> {
        await this.collection.doc(pigeon.id).set(pigeon);
    }
}
