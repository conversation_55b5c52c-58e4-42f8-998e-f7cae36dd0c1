import * as admin from "firebase-admin";
import { AnalysisJobRepository } from "../../domain/repositories/AnalysisJobRepository";
import { AnalysisJob, AnalysisJobDocument } from "../../domain/entities/AnalysisJob";

export class FirestoreAnalysisJobRepository implements AnalysisJobRepository {
    private collection = admin.firestore().collection("analysisJobs");

    async create(job: AnalysisJobDocument): Promise<void> {
        await this.collection.doc(job.captureId).set(job);
    }

    async update(job: AnalysisJobDocument): Promise<void> {
        await this.collection.doc(job.captureId).set(job, { merge: true });
    }

    async getByCaptureId(captureId: string): Promise<AnalysisJob | null> {
        const doc = await this.collection.doc(captureId).get();
        if (!doc.exists) {
            return null;
        }
        
        const data = doc.data() as AnalysisJobDocument;
        // Convert Firestore timestamps to Date objects
        const jobData = {
            ...data,
            createdAt: data.createdAt instanceof admin.firestore.Timestamp 
                ? data.createdAt.toDate() 
                : data.createdAt,
            updatedAt: data.updatedAt instanceof admin.firestore.Timestamp 
                ? data.updatedAt.toDate() 
                : data.updatedAt,
        };
        
        return AnalysisJob.fromDocument(jobData);
    }

    async getByTrainerId(trainerId: string, limit?: number, offset?: number): Promise<AnalysisJob[]> {
        const jobs: AnalysisJob[] = [];
        let query = this.collection
            .where("trainerId", "==", trainerId)
            .orderBy("createdAt", "desc");

        if (offset && offset > 0) {
            query = query.offset(offset);
        }

        if (limit && limit > 0) {
            query = query.limit(limit);
        }

        const snapshot = await query.get();

        snapshot.forEach((doc) => {
            const data = doc.data() as AnalysisJobDocument;
            // Convert Firestore timestamps to Date objects
            const jobData = {
                ...data,
                createdAt: data.createdAt instanceof admin.firestore.Timestamp 
                    ? data.createdAt.toDate() 
                    : data.createdAt,
                updatedAt: data.updatedAt instanceof admin.firestore.Timestamp 
                    ? data.updatedAt.toDate() 
                    : data.updatedAt,
            };
            
            jobs.push(AnalysisJob.fromDocument(jobData));
        });

        return jobs;
    }
}
