import { AnalysisJob } from "../domain/entities/AnalysisJob";
import { AnalysisJobStatus } from "../domain/enums/AnalysisJobStatus";
import { AnalysisJobErrorCode } from "../domain/enums/AnalysisJobErrorCode";

describe("AnalysisJob", () => {
    const mockDate = new Date("2025-01-01T00:00:00Z");
    const defaultJobData = {
        captureId: "test-capture-id",
        trainerId: "test-trainer-id",
        storageFilePath: "shots/test-trainer-id/test-capture-id/40.7128_-74.0060_test.jpg",
        status: AnalysisJobStatus.PENDING,
        errorCode: null,
        errorMessage: null,
        createdAt: mockDate,
        updatedAt: mockDate,
        pigeonId: null,
    };

    beforeAll(() => {
        jest.useFakeTimers();
        jest.setSystemTime(mockDate);
    });

    afterAll(() => {
        jest.useRealTimers();
    });

    describe("constructor", () => {
        it("should create an AnalysisJob with correct properties", () => {
            const job = new AnalysisJob(defaultJobData);

            expect(job.captureId).toBe("test-capture-id");
            expect(job.trainerId).toBe("test-trainer-id");
            expect(job.storageFilePath).toBe("shots/test-trainer-id/test-capture-id/40.7128_-74.0060_test.jpg");
            expect(job.status).toBe(AnalysisJobStatus.PENDING);
            expect(job.errorCode).toBeNull();
            expect(job.errorMessage).toBeNull();
            expect(job.createdAt).toEqual(mockDate);
            expect(job.updatedAt).toEqual(mockDate);
            expect(job.pigeonId).toBeNull();
        });
    });

    describe("markAsFinished", () => {
        it("should mark job as finished with pigeon ID", () => {
            const job = new AnalysisJob(defaultJobData);
            const pigeonId = "test-pigeon-id";

            job.markAsFinished(pigeonId);

            expect(job.status).toBe(AnalysisJobStatus.FINISHED);
            expect(job.pigeonId).toBe(pigeonId);
            expect(job.errorCode).toBeNull();
            expect(job.errorMessage).toBeNull();
            expect(job.updatedAt).toEqual(mockDate);
        });
    });

    describe("markAsError", () => {
        it("should mark job as error with error code and message", () => {
            const job = new AnalysisJob(defaultJobData);
            const errorCode = AnalysisJobErrorCode.NOT_A_BIRD;
            const errorMessage = "Image does not show a bird";

            job.markAsError(errorCode, errorMessage);

            expect(job.status).toBe(AnalysisJobStatus.ERROR);
            expect(job.errorCode).toBe(errorCode);
            expect(job.errorMessage).toBe(errorMessage);
            expect(job.pigeonId).toBeNull();
            expect(job.updatedAt).toEqual(mockDate);
        });
    });

    describe("updateStatus", () => {
        it("should update status and updatedAt timestamp", () => {
            const job = new AnalysisJob(defaultJobData);

            job.updateStatus(AnalysisJobStatus.FINISHED);

            expect(job.status).toBe(AnalysisJobStatus.FINISHED);
            expect(job.updatedAt).toEqual(mockDate);
        });
    });

    describe("toDocument", () => {
        it("should convert to document format", () => {
            const job = new AnalysisJob(defaultJobData);
            const document = job.toDocument();

            expect(document).toEqual({
                captureId: "test-capture-id",
                trainerId: "test-trainer-id",
                storageFilePath: "shots/test-trainer-id/test-capture-id/40.7128_-74.0060_test.jpg",
                status: AnalysisJobStatus.PENDING,
                errorCode: null,
                errorMessage: null,
                createdAt: mockDate,
                updatedAt: mockDate,
                pigeonId: null,
            });
        });
    });

    describe("fromDocument", () => {
        it("should create AnalysisJob from document", () => {
            const document = {
                captureId: "test-capture-id",
                trainerId: "test-trainer-id",
                storageFilePath: "shots/test-trainer-id/test-capture-id/40.7128_-74.0060_test.jpg",
                status: AnalysisJobStatus.FINISHED,
                errorCode: null,
                errorMessage: null,
                createdAt: mockDate,
                updatedAt: mockDate,
                pigeonId: "test-pigeon-id",
            };

            const job = AnalysisJob.fromDocument(document);

            expect(job.captureId).toBe("test-capture-id");
            expect(job.trainerId).toBe("test-trainer-id");
            expect(job.status).toBe(AnalysisJobStatus.FINISHED);
            expect(job.pigeonId).toBe("test-pigeon-id");
        });
    });
});
