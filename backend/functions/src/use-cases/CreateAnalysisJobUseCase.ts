import { <PERSON>Job } from "../domain/entities/AnalysisJob";
import { AnalysisJobStatus } from "../domain/enums/AnalysisJobStatus";
import { AnalysisJobRepository } from "../domain/repositories/AnalysisJobRepository";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { AnalysisJobErrorCode } from "../domain/enums/AnalysisJobErrorCode";

export interface CreateAnalysisJobRequest {
    captureId: string;
    trainerId: string;
    storageFilePath: string;
}

export class CreateAnalysisJobUseCase {
    constructor(
        private analysisJobRepo: AnalysisJobRepository,
        private trainerRepo: TrainerRepository,
    ) {}

    async execute(request: CreateAnalysisJobRequest): Promise<AnalysisJob> {
        // Validate that the trainer exists
        const trainer = await this.trainerRepo.getById(request.trainerId);
        if (!trainer) {
            throw new Error("Trainer not found");
        }

        // Check if trainer has capture stock
        if (trainer.pigeonBalls <= 0) {
            throw new Error("No capture stock available");
        }

        // Check if a job with this captureId already exists
        const existingJob = await this.analysisJobRepo.getByCaptureId(request.captureId);
        if (existingJob) {
            throw new Error("Analysis job already exists for this capture");
        }

        // Create the analysis job
        const now = new Date();
        const analysisJob = new AnalysisJob({
            captureId: request.captureId,
            trainerId: request.trainerId,
            storageFilePath: request.storageFilePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: now,
            updatedAt: now,
            pigeonId: null,
        });

        await this.analysisJobRepo.create(analysisJob.toDocument());
        return analysisJob;
    }
}
