import { AnalysisJobStatus } from "../enums/AnalysisJobStatus";
import { AnalysisJobErrorCode } from "../enums/AnalysisJobErrorCode";

export type AnalysisJobData = Pick<
    AnalysisJob,
    | "captureId"
    | "trainerId"
    | "storageFilePath"
    | "status"
    | "errorCode"
    | "errorMessage"
    | "createdAt"
    | "updatedAt"
    | "pigeonId"
>;

export class AnalysisJob {
    captureId: string;
    trainerId: string;
    storageFilePath: string;
    status: AnalysisJobStatus;
    errorCode: AnalysisJobErrorCode | null;
    errorMessage: string | null;
    createdAt: Date;
    updatedAt: Date;
    pigeonId: string | null; // Set when the pigeon is successfully created

    constructor(data: AnalysisJobData) {
        this.captureId = data.captureId;
        this.trainerId = data.trainerId;
        this.storageFilePath = data.storageFilePath;
        this.status = data.status;
        this.errorCode = data.errorCode;
        this.errorMessage = data.errorMessage;
        this.createdAt = data.createdAt;
        this.updatedAt = data.updatedAt;
        this.pigeonId = data.pigeonId;
    }

    markAsFinished(pigeonId: string): void {
        this.status = AnalysisJobStatus.FINISHED;
        this.pigeonId = pigeonId;
        this.errorCode = null;
        this.errorMessage = null;
        this.updatedAt = new Date();
    }

    markAsError(errorCode: AnalysisJobErrorCode, errorMessage: string): void {
        this.status = AnalysisJobStatus.ERROR;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.pigeonId = null;
        this.updatedAt = new Date();
    }

    updateStatus(status: AnalysisJobStatus): void {
        this.status = status;
        this.updatedAt = new Date();
    }

    toDocument(): AnalysisJobDocument {
        return {
            captureId: this.captureId,
            trainerId: this.trainerId,
            storageFilePath: this.storageFilePath,
            status: this.status,
            errorCode: this.errorCode,
            errorMessage: this.errorMessage,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            pigeonId: this.pigeonId,
        };
    }

    static fromDocument(doc: AnalysisJobDocument): AnalysisJob {
        return new AnalysisJob(doc);
    }
}

export type AnalysisJobDocument = {
    captureId: string;
    trainerId: string;
    storageFilePath: string;
    status: AnalysisJobStatus;
    errorCode: AnalysisJobErrorCode | null;
    errorMessage: string | null;
    createdAt: Date;
    updatedAt: Date;
    pigeonId: string | null;
};
